# UnsecuredAPIKeys - System Architecture Documentation

## Overview
This document provides comprehensive technical diagrams and architecture documentation for the UnsecuredAPIKeys security scanner application, designed for academic and technical reference purposes.

## 1. System Architecture Diagram

### Architecture Layers

```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                       │
├─────────────────────────────────────────────────────────────┤
│ Next.js 14 Frontend (TypeScript)                           │
│ - React Components with TypeScript                         │
│ - Tailwind CSS for styling                                 │
│ - Supabase Auth integration                                │
│ - Real-time updates via SignalR                           │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    APPLICATION LAYER                        │
├─────────────────────────────────────────────────────────────┤
│ .NET 9 Web API (C#)                                       │
│ - RESTful API Controllers                                  │
│ - SignalR Hubs for real-time communication               │
│ - Authentication & Authorization                           │
│ - Rate Limiting & Security Middleware                     │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    BUSINESS LOGIC LAYER                     │
├─────────────────────────────────────────────────────────────┤
│ Security Scanning Services                                 │
│ - Repository Analysis Engine                               │
│ - Pattern Recognition Algorithms                           │
│ - Severity Classification System                           │
│ - False Positive Reduction Logic                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    DATA ACCESS LAYER                        │
├─────────────────────────────────────────────────────────────┤
│ Entity Framework Core 9                                   │
│ - Repository Pattern Implementation                        │
│ - Database Context & Migrations                           │
│ - LINQ Query Optimization                                 │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    PERSISTENCE LAYER                        │
├─────────────────────────────────────────────────────────────┤
│ PostgreSQL 15 Database                                    │
│ - Scan Results Storage                                    │
│ - User Management Tables                                  │
│ - Analytics & Metrics Data                               │
└─────────────────────────────────────────────────────────────┘
```

### External Integrations

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Next.js   │◄──►│  .NET API   │◄──►│ PostgreSQL  │
│  Frontend   │    │   Backend   │    │  Database   │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Supabase   │    │   GitHub    │    │   Render    │
│    Auth     │    │     API     │    │  Hosting    │
└─────────────┘    └─────────────┘    └─────────────┘
```

### Technology Stack Breakdown

**Frontend Technologies:**
- Next.js 14.0+ (React Framework)
- React 18+ (UI Library)
- TypeScript 5.0+ (Type Safety)
- Tailwind CSS 3.0+ (Styling)
- HeroUI (Component Library)
- Framer Motion (Animations)

**Backend Technologies:**
- .NET 9 (Runtime)
- ASP.NET Core Web API (Web Framework)
- SignalR (Real-time Communication)
- Entity Framework Core 9 (ORM)
- C# 12 (Programming Language)

**Database & Storage:**
- PostgreSQL 15 (Primary Database)
- Entity Framework Migrations (Schema Management)
- Connection Pooling (Performance Optimization)

**External Services:**
- GitHub REST API v4 (Repository Access)
- Octokit.NET (GitHub API Client)
- Supabase Auth (Authentication)
- Render.com (Backend Hosting)
- Netlify (Frontend Hosting)

**Development Tools:**
- Docker & Docker Compose (Containerization)
- GitHub Actions (CI/CD)
- Sentry (Error Tracking)
- ESLint & Prettier (Code Quality)

## 2. User Flow Diagram

### Primary User Journey

```
START → GitHub OAuth → Dashboard → Repository Input → Scanning Process → Results → Action
  │         │             │            │               │            │        │
  │         │             │            │               │            │        └─→ Export/Share
  │         │             │            │               │            └─→ View Details
  │         │             │            │               └─→ Real-time Progress
  │         │             │            └─→ Validation & Rate Limiting
  │         │             └─→ Recent Scans History
  │         └─→ Supabase Authentication
  └─→ Landing Page Education
```

### Detailed Step-by-Step Flow

1. **Landing Page**
   - User learns about security scanning
   - Educational content about API key risks
   - Call-to-action to start scanning

2. **Authentication**
   - GitHub OAuth via Supabase
   - User consent for repository access
   - Session establishment

3. **Dashboard**
   - Overview of recent scans
   - Usage statistics
   - Quick scan initiation

4. **Repository Input**
   - User enters GitHub repository URL
   - Repository validation
   - Access permission checks

5. **Scanning Process**
   - Real-time progress updates via SignalR
   - File analysis and pattern matching
   - Security vulnerability detection

6. **Results Display**
   - Categorized security findings
   - Severity level classification
   - Detailed vulnerability information

7. **Action Options**
   - Export results to various formats
   - Share findings with team members
   - Schedule follow-up scans

### Decision Points and Alternative Paths

**Authentication Paths:**
- Authenticated User → Full feature access
- Guest User → Limited scanning capabilities
- Rate Limited User → Upgrade prompts

**Repository Access:**
- Public Repository → Direct scanning
- Private Repository → OAuth permission required
- Inaccessible Repository → Error handling and suggestions

**Scan Results:**
- Critical Findings → Immediate alerts and recommendations
- Clean Repository → Success confirmation and best practices
- Partial Results → Warning about incomplete scan

## 3. Security Scanning Architecture

### Scanning Engine Components

```
┌─────────────────────────────────────────────────────────────┐
│                    SCANNING ORCHESTRATOR                    │
├─────────────────────────────────────────────────────────────┤
│ - Job Queue Management                                     │
│ - Progress Tracking                                        │
│ - Result Aggregation                                       │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    PATTERN RECOGNITION ENGINE               │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│ │ API Key     │ │ Password    │ │ Token       │           │
│ │ Scanner     │ │ Scanner     │ │ Scanner     │           │
│ └─────────────┘ └─────────────┘ └─────────────┘           │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│ │ Environment │ │ Private Key │ │ Certificate │           │
│ │ Scanner     │ │ Scanner     │ │ Scanner     │           │
│ └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    FALSE POSITIVE FILTER                    │
├─────────────────────────────────────────────────────────────┤
│ - Context Analysis                                         │
│ - Pattern Validation                                       │
│ - Confidence Scoring                                       │
└─────────────────────────────────────────────────────────────┘
```

### Security Scanner Types

**API Key Scanner:**
- Detects exposed API keys from major providers
- Patterns for AWS, Google Cloud, OpenAI, Stripe, etc.
- Severity: High to Critical

**Password Scanner:**
- Identifies hardcoded passwords
- Detects weak/default passwords
- Context-aware filtering
- Severity: Medium to High

**Token Scanner:**
- Finds JWT tokens and access tokens
- Detects private keys and certificates
- OAuth token identification
- Severity: High to Critical

**Environment File Scanner:**
- Scans .env files and configuration files
- Identifies sensitive environment variables
- Checks for committed secrets
- Severity: Medium to Critical

## 4. Database Schema Design

### Core Entities

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Users       │    │   ScanResults   │    │ SecurityFindings│
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ Id (PK)         │◄──►│ Id (PK)         │◄──►│ Id (PK)         │
│ Email           │    │ UserId (FK)     │    │ ScanResultId(FK)│
│ GitHubId        │    │ RepositoryUrl   │    │ Type            │
│ CreatedAt       │    │ Status          │    │ Severity        │
│ LastLoginAt     │    │ StartedAt       │    │ FilePath        │
│ SubscriptionTier│    │ CompletedAt     │    │ LineNumber      │
└─────────────────┘    │ FindingsCount   │    │ Value           │
                       │ IsPublic        │    │ Context         │
                       └─────────────────┘    │ Confidence      │
                                              └─────────────────┘
```

### Relationships

- Users (1) → ScanResults (Many)
- ScanResults (1) → SecurityFindings (Many)
- Users can have multiple scan results
- Each scan result can have multiple security findings

## 5. API Architecture

### RESTful Endpoints

```
GET    /api/scan/recent           # Get recent scans
POST   /api/scan/start            # Start new scan
GET    /api/scan/{id}             # Get scan details
DELETE /api/scan/{id}             # Delete scan
GET    /api/scan/{id}/findings    # Get scan findings
POST   /api/scan/{id}/export      # Export scan results
GET    /api/user/profile          # Get user profile
PUT    /api/user/preferences      # Update preferences
GET    /api/analytics/stats       # Get usage statistics
```

### SignalR Hubs

```
ScanProgressHub
├── JoinScanGroup(scanId)
├── LeaveScanGroup(scanId)
├── OnScanStarted(scanId, repositoryUrl)
├── OnScanProgress(scanId, progress, currentFile)
├── OnScanCompleted(scanId, findingsCount)
└── OnScanError(scanId, errorMessage)
```

## 6. Deployment Architecture

### Production Environment

```
┌─────────────────────────────────────────────────────────────┐
│                    NETLIFY CDN                              │
├─────────────────────────────────────────────────────────────┤
│ Next.js Static Site                                        │
│ - Global CDN Distribution                                  │
│ - Automatic HTTPS                                          │
│ - Branch Previews                                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    RENDER.COM                               │
├─────────────────────────────────────────────────────────────┤
│ .NET Web API Service                                       │
│ - Auto-scaling                                             │
│ - Health Checks                                            │
│ - Environment Variables                                    │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    RENDER POSTGRESQL                        │
├─────────────────────────────────────────────────────────────┤
│ Managed Database Service                                   │
│ - Automated Backups                                        │
│ - Connection Pooling                                       │
│ - SSL Encryption                                           │
└─────────────────────────────────────────────────────────────┘
```

### Development Environment

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   localhost:3000│    │   localhost:7227│    │   localhost:5432│
│   Next.js Dev   │◄──►│   .NET API      │◄──►│   PostgreSQL    │
│   Server        │    │   Development   │    │   Docker        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 7. Security Architecture

### Authentication Flow

```
User → Frontend → Supabase Auth → GitHub OAuth → Token Exchange → API Access
  │        │           │              │              │             │
  │        │           │              │              │             └─→ JWT Validation
  │        │           │              │              └─→ Session Creation
  │        │           │              └─→ User Consent
  │        │           └─→ OAuth Redirect
  │        └─→ Login Initiation
  └─→ User Action
```

### Security Measures

**Frontend Security:**
- Content Security Policy (CSP)
- HTTPS enforcement
- XSS protection
- CSRF tokens

**API Security:**
- JWT token validation
- Rate limiting
- Input validation
- SQL injection prevention
- CORS configuration

**Database Security:**
- Connection string encryption
- Parameterized queries
- Access control
- Audit logging

## 8. Performance Optimization

### Caching Strategy

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Browser       │    │   CDN Cache     │    │   API Cache     │
│   Cache         │    │   (Static)      │    │   (Redis)       │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ - Static Assets │    │ - Images        │    │ - Scan Results  │
│ - API Responses │    │ - CSS/JS        │    │ - User Data     │
│ - User Sessions │    │ - Fonts         │    │ - Analytics     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Database Optimization

- Indexed columns for frequent queries
- Connection pooling
- Query optimization
- Pagination for large result sets
- Async operations for I/O bound tasks

## 9. Monitoring and Analytics

### Application Monitoring

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Sentry        │    │   Application   │    │   Database      │
│   Error         │◄───│   Logs          │◄───│   Metrics       │
│   Tracking      │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────┐
│                    MONITORING DASHBOARD                     │
├─────────────────────────────────────────────────────────────┤
│ - Error Rates and Trends                                   │
│ - Performance Metrics                                      │
│ - User Activity Analytics                                  │
│ - System Health Status                                     │
└─────────────────────────────────────────────────────────────┘
```

## 10. Diagram Creation Instructions

### Recommended Tools

**Primary: Draw.io (diagrams.net)**
- Free, web-based
- Professional templates
- Export to PNG, SVG, PDF
- Academic-friendly

**Alternative Tools:**
- Lucidchart (professional features)
- Figma (UI/UX focused)
- PlantUML (code-based diagrams)

### Visual Design Guidelines

**Color Coding:**
- Blue: Frontend components
- Green: Backend services
- Orange: Databases
- Gray: External services
- Red: Security components

**Shape Standards:**
- Rectangles: Services/Applications
- Cylinders: Databases
- Clouds: External services
- Diamonds: Decision points
- Circles: Start/End points

### Export Settings for Academic Use

- **PNG**: 300 DPI for print quality
- **SVG**: For scalable web use
- **PDF**: For direct report inclusion
- **Size**: Minimum 1200px width for clarity

## Conclusion

This architecture documentation provides a comprehensive technical overview of the UnsecuredAPIKeys security scanner application. The multi-tier architecture ensures scalability, maintainability, and security while providing a seamless user experience for developers seeking to secure their repositories.

The system's design emphasizes real-time communication, robust security scanning capabilities, and user-friendly interfaces, making it an effective tool for preventing security vulnerabilities in software development workflows.
